'use client';

import { useRef, useState, useEffect, memo, useMemo, useCallback } from 'react';
import { usePomodoroStore, type Video } from '@/lib/pomodoro-store';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Music2, ChevronLeft, ChevronRight, Maximize, Settings, CheckSquare, Clock, BarChart3 } from 'lucide-react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { QuickStatsDialog } from './quick-stats-dialog';
import { TimerSettings } from '@/components/settings/timer-setting/timer-settings-dialog';
import { TaskSheet } from '@/components/task-management';
import { VideoTitleIndicator } from './video-title-indicator';
import { useGetPomodoroQuickStats } from '../../../../prisma/schema/Pomodoro/pomodoro-query';
import { useUserStore } from '@/store/userStore';

interface NavigationControlsProps {
  onBackNavigation?: () => void;
}

export const NavigationControls = memo(function NavigationControls({ onBackNavigation }: NavigationControlsProps) {
  const selectedVideo = usePomodoroStore((state) => state.selectedVideo);
  const navigableContent = usePomodoroStore((state) => state.navigableContent);
  const isNavigating = usePomodoroStore((state) => state.isNavigating);
  const isVideoSwitching = usePomodoroStore((state) => state.isVideoSwitching);
  const setVideoSwitching = usePomodoroStore((state) => state.setVideoSwitching);
  const getCurrentContent = usePomodoroStore((state) => state.getCurrentContent);
  const showCurrentTime = usePomodoroStore((state) => state.showCurrentTime);

  const [showControls, setShowControls] = useState(true);
  const [isAudioControlsVisible, setIsAudioControlsVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [contentIndicatorReady, setContentIndicatorReady] = useState(false);
  const [touchStartX, setTouchStartX] = useState<number | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);
  const [lastTouchTime, setLastTouchTime] = useState(0);
  const [isControlsLocked, setIsControlsLocked] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [isTaskSheetOpen, setIsTaskSheetOpen] = useState(false);
  const [isStatsPopoverOpen, setIsStatsPopoverOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState('');
  const [isTransitioning, setIsTransitioning] = useState(false);

  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const audioToggleRef = useRef<HTMLButtonElement>(null);
  const transitionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isAuthenticated } = useUserStore();

  // Calculate date ranges in user's timezone for API calls
  const calculateDateRanges = () => {
    const now = new Date();

    // Today's range
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart);
    todayEnd.setDate(todayEnd.getDate() + 1);

    // Week range (Monday to Sunday)
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // If Sunday, go back 6 days to Monday
    const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    weekStart.setDate(weekStart.getDate() - daysFromMonday);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    // Month range (1st to last day of current month)
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    return {
      todayStart: todayStart.toISOString(),
      todayEnd: todayEnd.toISOString(),
      weekStart: weekStart.toISOString(),
      weekEnd: weekEnd.toISOString(),
      monthStart: monthStart.toISOString(),
      monthEnd: monthEnd.toISOString(),
    };
  };

  const dateRanges = calculateDateRanges();

  // Prefetch stats data for authenticated users
  const { data: quickStatsData, isLoading: statsLoading, error: statsError } = useGetPomodoroQuickStats(isAuthenticated, dateRanges);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                           window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Set content indicator as ready when component mounts
  useEffect(() => {
    setContentIndicatorReady(true);
  }, []);

  // Update current time every second
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      setCurrentTime(timeString);
    };

    // Update immediately
    updateTime();

    // Set up interval to update every second
    const interval = setInterval(updateTime, 1000);

    return () => {
      clearInterval(interval);
      // Cleanup transition timeout on unmount
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
    };
  }, []);

  // Debounced setShowControls to prevent flickering
  const debouncedSetShowControls = useCallback((value: boolean) => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setShowControls(value);

    // Clear any existing transition timeout
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    // Reset transition state after animation completes
    transitionTimeoutRef.current = setTimeout(() => {
      setIsTransitioning(false);
    }, 400); // Match the longest animation duration
  }, [isTransitioning]);

  // Check if any navigation popover/dialog is open
  const isAnyPopoverOpen = useCallback(() => {
    return isSettingsDialogOpen || isTaskSheetOpen || isStatsPopoverOpen || isAudioControlsVisible;
  }, [isSettingsDialogOpen, isTaskSheetOpen, isStatsPopoverOpen, isAudioControlsVisible]);

  // Filter only video content for navigation
  const videoOnlyContent = useMemo(() => {
    return navigableContent.filter(content => content.type === 'video');
  }, [navigableContent]);

  // Find current video index
  const currentVideoIndex = useMemo(() => {
    if (!selectedVideo) return -1;
    return videoOnlyContent.findIndex(content => content.id === selectedVideo.id);
  }, [videoOnlyContent, selectedVideo]);

  // Get current content for display
  const currentContent = getCurrentContent();
  const canNavigate = videoOnlyContent.length > 1;
  const hasPrevious = canNavigate;
  const hasNext = canNavigate;

  // Handle fullscreen functionality
  const handleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, []);

  // Function to preload next video and handle seamless transition
  const preloadNextVideo = useCallback((nextVideo: Video) => {
    // Create a new video element for preloading
    const preloadVideo = document.createElement('video');
    preloadVideo.src = nextVideo.src;
    preloadVideo.autoplay = false;
    preloadVideo.loop = true;
    preloadVideo.muted = true; // Will be updated based on audio settings
    preloadVideo.playsInline = true;
    preloadVideo.preload = 'auto';

    // Preload thumbnail
    const preloadThumbnail = new Image();
    preloadThumbnail.src = nextVideo.thumbnail;

    let thumbnailLoaded = false;
    let videoLoaded = false;

    const checkIfReady = () => {
      if (thumbnailLoaded && videoLoaded) {
        // Both are ready, switch to new video
        usePomodoroStore.getState().selectVideo(nextVideo);
        // Reset switching state after a short delay
        setTimeout(() => setVideoSwitching(false), 500);
      }
    };

    preloadThumbnail.onload = () => {
      thumbnailLoaded = true;
      checkIfReady();
    };

    preloadThumbnail.onerror = () => {
      thumbnailLoaded = true; // Continue even with thumbnail error
      checkIfReady();
    };

    preloadVideo.oncanplay = () => {
      if (preloadVideo.readyState >= 3) {
        videoLoaded = true;
        checkIfReady();
      }
    };

    preloadVideo.onerror = () => {
      // Handle video error - fallback to normal switching
      setVideoSwitching(false);
      usePomodoroStore.getState().selectVideo(nextVideo);
    };
  }, [setVideoSwitching]);

  // Navigation functions
  const navigateToNextVideo = useCallback(() => {
    if (videoOnlyContent.length <= 1 || isVideoSwitching) return;

    const nextIndex = (currentVideoIndex + 1) % videoOnlyContent.length;
    const nextVideo = videoOnlyContent[nextIndex].video;
    if (nextVideo) {
      setVideoSwitching(true);
      preloadNextVideo(nextVideo);
    }
  }, [videoOnlyContent, currentVideoIndex, isVideoSwitching, setVideoSwitching, preloadNextVideo]);

  const navigateToPreviousVideo = useCallback(() => {
    if (videoOnlyContent.length <= 1 || isVideoSwitching) return;

    const prevIndex = (currentVideoIndex - 1 + videoOnlyContent.length) % videoOnlyContent.length;
    const prevVideo = videoOnlyContent[prevIndex].video;
    if (prevVideo) {
      setVideoSwitching(true);
      preloadNextVideo(prevVideo);
    }
  }, [videoOnlyContent, currentVideoIndex, isVideoSwitching, setVideoSwitching, preloadNextVideo]);

  // Mobile touch gesture handling
  const handleTouchStart = useCallback((e: TouchEvent) => {
    // Don't handle touch gestures if any dialogs/sheets are open
    const hasOpenDialog = document.querySelector('[data-state="open"][role="dialog"]') ||
                         document.querySelector('[data-state="open"][data-radix-dialog-content]') ||
                         isAnyPopoverOpen();

    if (hasOpenDialog) {
      return;
    }

    const touch = e.touches[0];
    setTouchStartX(touch.clientX);
    setTouchStartY(touch.clientY);
    setLastTouchTime(Date.now());
  }, [isAnyPopoverOpen]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!touchStartX || !touchStartY) return;

    // Don't handle touch gestures if any dialogs/sheets are open
    const hasOpenDialog = document.querySelector('[data-state="open"][role="dialog"]') ||
                         document.querySelector('[data-state="open"][data-radix-dialog-content]') ||
                         isAnyPopoverOpen();

    if (hasOpenDialog) {
      setTouchStartX(null);
      setTouchStartY(null);
      return;
    }

    const touch = e.changedTouches[0];
    const touchEndX = touch.clientX;
    const touchEndY = touch.clientY;
    const touchDuration = Date.now() - lastTouchTime;

    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // Check if it's a swipe gesture (horizontal movement > vertical movement)
    if (absDeltaX > absDeltaY && absDeltaX > 50) {
      if (deltaX > 0) {
        navigateToPreviousVideo();
      } else {
        navigateToNextVideo();
      }
    } 
    // Check for tap gesture to toggle controls
    else if (absDeltaX < 10 && absDeltaY < 10 && touchDuration < 300) {
      const timerCard = document.querySelector('[role="region"][aria-label*="timer"]');
      const tapTarget = document.elementFromPoint(touchEndX, touchEndY);
      
      if (timerCard && tapTarget && !timerCard.contains(tapTarget)) {
        // Check if the tap is on a dropdown menu element or its descendants
        const isDropdownMenuElement = tapTarget && (
          tapTarget.closest('[data-slot="dropdown-menu"]') ||
          tapTarget.closest('[data-slot="dropdown-menu-trigger"]') ||
          tapTarget.closest('[data-slot="dropdown-menu-content"]') ||
          tapTarget.closest('[data-slot="dropdown-menu-item"]') ||
          tapTarget.closest('[data-radix-dropdown-menu-trigger]') ||
          tapTarget.closest('[data-radix-dropdown-menu-content]') ||
          tapTarget.closest('[role="menu"]') ||
          tapTarget.closest('[role="menuitem"]')
        );

        // If it's a dropdown menu element, don't interfere with the interaction
        if (isDropdownMenuElement) {
          setTouchStartX(null);
          setTouchStartY(null);
          return;
        }

        const isButtonTap = tapTarget && (
          tapTarget.closest('button') ||
          tapTarget.closest('[role="button"]') ||
          tapTarget.closest('a')
        );
        
        if (isButtonTap) {
          if (!isTransitioning) {
            debouncedSetShowControls(true);
          }
          setIsControlsLocked(false);

          if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
          }

          controlsTimeoutRef.current = setTimeout(() => {
            if (!isControlsLocked && !isTransitioning) {
              debouncedSetShowControls(false);
            }
          }, 6000);
        } else {
          const newShowControls = !showControls;
          setIsControlsLocked(newShowControls);

          if (!isTransitioning) {
            debouncedSetShowControls(newShowControls);
          }

          if (newShowControls) {
            if (controlsTimeoutRef.current) {
              clearTimeout(controlsTimeoutRef.current);
            }
            controlsTimeoutRef.current = setTimeout(() => {
              if (!isControlsLocked && !isTransitioning) {
                debouncedSetShowControls(false);
                setIsControlsLocked(false);
              }
            }, 4000);
          }
        }
      }
    }

    setTouchStartX(null);
    setTouchStartY(null);
  }, [touchStartX, touchStartY, lastTouchTime, navigateToNextVideo, navigateToPreviousVideo, isControlsLocked, isAnyPopoverOpen, debouncedSetShowControls, isTransitioning, showControls]);

  // Controls management
  useEffect(() => {
    if (isMobile) {
      document.addEventListener('touchstart', handleTouchStart, { passive: true });
      document.addEventListener('touchend', handleTouchEnd, { passive: true });

      if (!isControlsLocked) {
        controlsTimeoutRef.current = setTimeout(() => {
          if (!isTransitioning) {
            debouncedSetShowControls(false);
          }
        }, 4000);
      }

      return () => {
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    } else {
      const handleMouseMove = () => {
        if (!isTransitioning) {
          debouncedSetShowControls(true);
        }

        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current);
        }

        // Check if any dropdown menus or popovers are open before setting timeout
        const openDropdowns = document.querySelectorAll('[data-state="open"][data-slot*="dropdown-menu"]');

        if (!isAnyPopoverOpen() && openDropdowns.length === 0) {
          controlsTimeoutRef.current = setTimeout(() => {
            if (!isTransitioning) {
              debouncedSetShowControls(false);
            }
          }, 3000);
        }
      };

      // Check for open dropdowns and popovers when setting initial timeout
      const openDropdowns = document.querySelectorAll('[data-state="open"][data-slot*="dropdown-menu"]');

      if (!isAnyPopoverOpen() && openDropdowns.length === 0) {
        controlsTimeoutRef.current = setTimeout(() => {
          if (!isTransitioning) {
            debouncedSetShowControls(false);
          }
        }, 3000);
      }

      window.addEventListener('mousemove', handleMouseMove);

      return () => {
        window.removeEventListener('mousemove', handleMouseMove);
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current);
        }
      };
    }
  }, [isMobile, isAnyPopoverOpen, isControlsLocked, handleTouchStart, handleTouchEnd, isTransitioning, debouncedSetShowControls]);

  // Handle keyboard navigation (desktop only)
  useEffect(() => {
    if (!isMobile) {
      const handleKeyDown = (e: KeyboardEvent) => {
        // Check if the user is currently typing in a form element
        const target = e.target as HTMLElement;
        const isTypingInForm = target && (
          target.tagName === 'INPUT' ||
          target.tagName === 'TEXTAREA' ||
          target.tagName === 'SELECT' ||
          target.isContentEditable ||
          target.closest('[contenteditable="true"]') ||
          target.closest('input') ||
          target.closest('textarea') ||
          target.closest('select')
        );

        // If user is typing in a form element, don't handle keyboard shortcuts
        if (isTypingInForm) {
          return;
        }

        if (e.key === 'f' || e.key === 'F') {
          e.preventDefault();
          handleFullscreen();
        } else if (e.key === 'ArrowLeft' && hasPrevious) {
          e.preventDefault();
          navigateToPreviousVideo();
        } else if (e.key === 'ArrowRight' && hasNext) {
          e.preventDefault();
          navigateToNextVideo();
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isMobile, handleFullscreen, hasPrevious, hasNext, navigateToPreviousVideo, navigateToNextVideo]);

  // Audio controls toggle
  const toggleAudioControls = () => {
    const newVisible = !isAudioControlsVisible;
    setIsAudioControlsVisible(newVisible);
    if (!isTransitioning) {
      debouncedSetShowControls(true);
    }

    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    window.dispatchEvent(new CustomEvent('audio-controls-toggle', {
      detail: { visible: newVisible }
    }));
  };

  // Listen for audio controls toggle events from the music control wrapper
  useEffect(() => {
    const handleAudioToggle = (event: CustomEvent<{ visible: boolean }>) => {
      // Only update state if it's different from current state to prevent loops
      if (event.detail.visible !== isAudioControlsVisible) {
        setIsAudioControlsVisible(event.detail.visible);

        // If audio controls are closed, resume normal controls timeout behavior
        if (!event.detail.visible) {
          if (!isTransitioning) {
            debouncedSetShowControls(true);
          }

          if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
          }

          // Resume auto-hide behavior for desktop if no other popovers are open
          if (!isMobile && !isAnyPopoverOpen()) {
            controlsTimeoutRef.current = setTimeout(() => {
              if (!isTransitioning) {
                debouncedSetShowControls(false);
              }
            }, 3000);
          }
        }
      }
    };

    window.addEventListener('audio-controls-toggle', handleAudioToggle as EventListener);

    return () => {
      window.removeEventListener('audio-controls-toggle', handleAudioToggle as EventListener);
    };
  }, [isMobile, isAudioControlsVisible, isTransitioning, debouncedSetShowControls, isAnyPopoverOpen]);

  // Listen for dropdown menu state changes to manage controls timing
  useEffect(() => {
    const handleDropdownStateChange = () => {
      const openDropdowns = document.querySelectorAll('[data-state="open"][data-slot*="dropdown-menu"]');
      
      if (openDropdowns.length > 0) {
        // Dropdown is open, clear any pending hide timeout
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current);
          controlsTimeoutRef.current = null;
        }
        if (!isTransitioning) {
          debouncedSetShowControls(true);
        }
      } else {
        // No dropdowns are open, resume normal timeout behavior if not locked and no popovers are open
        if (!isControlsLocked && !isAnyPopoverOpen()) {
          controlsTimeoutRef.current = setTimeout(() => {
            if (!isTransitioning) {
              debouncedSetShowControls(false);
            }
          }, isMobile ? 4000 : 3000);
        }
      }
    };

    // Set up a mutation observer to watch for dropdown state changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            mutation.attributeName === 'data-state' &&
            (mutation.target as Element).hasAttribute('data-slot') &&
            (mutation.target as Element).getAttribute('data-slot')?.includes('dropdown-menu')) {
          handleDropdownStateChange();
        }
      });
    });

    // Start observing the document for dropdown state changes
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-state'],
      subtree: true
    });

    return () => {
      observer.disconnect();
    };
  }, [isControlsLocked, isAnyPopoverOpen, isMobile, isTransitioning, debouncedSetShowControls]);

  // Settings dialog toggle
  const toggleSettingsDialog = () => {
    setIsSettingsDialogOpen(true);
  };

  const handleSettingsDialogClose = (open: boolean) => {
    setIsSettingsDialogOpen(open);
  };

  // Task sheet toggle
  const toggleTaskSheet = () => {
    setIsTaskSheetOpen(!isTaskSheetOpen);
  };

  if (!selectedVideo) {
    return null;
  }

  return (
    <>
      {/* Back button - Always top left */}
      <div
        className={`fixed top-3 sm:top-4 left-3 sm:left-4 z-50 transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {onBackNavigation ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.div
                whileHover={!isMobile ? {
                  scale: 1.1,
                  boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                  backgroundColor: "rgba(255, 255, 255, 0.12)"
                } : {}}
                whileTap={{
                  scale: 0.95,
                  backgroundColor: "rgba(255, 255, 255, 0.18)",
                  rotate: isMobile ? 0 : -90
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 17,
                  rotate: {
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                  }
                }}
                className="rounded-full overflow-hidden"
              >
                <Button
                  variant="outline"
                  size={isMobile ? "default" : "icon"}
                  className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 cursor-pointer ${
                    isMobile ? 'h-9 w-9 p-0' : ''
                  }`}
                  onClick={onBackNavigation}
                >
                  <ArrowLeft className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                </Button>
              </motion.div>
            </TooltipTrigger>
            <TooltipContent side="right" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
              <span className="font-medium tracking-wide">Return to home</span>
            </TooltipContent>
          </Tooltip>
        ) : (
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.div
                whileHover={!isMobile ? {
                  scale: 1.1,
                  boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                  backgroundColor: "rgba(255, 255, 255, 0.12)"
                } : {}}
                whileTap={{
                  scale: 0.95,
                  backgroundColor: "rgba(255, 255, 255, 0.18)",
                  rotate: isMobile ? 0 : -90
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 17,
                  rotate: {
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                  }
                }}
                className="rounded-full overflow-hidden"
              >
                <Link href="/">
                  <Button
                    variant="outline"
                    size={isMobile ? "default" : "icon"}
                    className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 cursor-pointer ${
                      isMobile ? 'h-9 w-9 p-0' : ''
                    }`}
                  >
                    <ArrowLeft className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                  </Button>
                </Link>
              </motion.div>
            </TooltipTrigger>
            <TooltipContent side="right" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
              <span className="font-medium tracking-wide">Return to home</span>
            </TooltipContent>
          </Tooltip>
        )}
      </div>

      {/* Video Title Indicator */}
      <VideoTitleIndicator
        currentContent={currentContent}
        canNavigate={canNavigate}
        contentIndicatorReady={contentIndicatorReady}
        isMobile={isMobile}
        showControls={showControls}
      />

      {/* Top right area - Either control buttons or current time display */}
      <div className="fixed top-3 sm:top-4 right-3 sm:right-4 z-50">
        <AnimatePresence mode="wait">
          {showControls ? (
            <motion.div
              key="navigation-controls"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{
                duration: 0.35,
                ease: [0.4, 0, 0.2, 1] // Custom easing for smoother transitions
              }}
              className="flex gap-2"
            >

        {/* Task button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={!isMobile ? {
                scale: 1.1,
                boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                backgroundColor: "rgba(255, 255, 255, 0.12)"
              } : {}}
              whileTap={{
                scale: 0.95,
                backgroundColor: "rgba(255, 255, 255, 0.18)",
                rotate: isMobile ? 0 : 90
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17,
                rotate: {
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }
              }}
              className="rounded-full overflow-hidden"
            >
              <Button
                variant="outline"
                size={isMobile ? "default" : "icon"}
                className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 group cursor-pointer ${
                  isTaskSheetOpen ? 'bg-neutral-300/30 dark:bg-neutral-400/30' : ''
                } ${isMobile ? 'h-9 w-9 p-0' : ''}`}
                onClick={toggleTaskSheet}
              >
                <span className="relative">
                  <CheckSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                </span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent side="left" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
            <span className="font-medium tracking-wide">Tasks</span>
          </TooltipContent>
        </Tooltip>

        {/* Audio toggle icon */}
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={!isMobile ? {
                scale: 1.1,
                boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                backgroundColor: "rgba(255, 255, 255, 0.12)"
              } : {}}
              whileTap={{
                scale: 0.95,
                backgroundColor: "rgba(255, 255, 255, 0.18)",
                rotate: isMobile ? 0 : 90
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17,
                rotate: {
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }
              }}
              className="rounded-full overflow-hidden"
            >
              <Button
                ref={audioToggleRef}
                variant="outline"
                size={isMobile ? "default" : "icon"}
                className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 group cursor-pointer ${
                  isAudioControlsVisible ? 'bg-neutral-300/30 dark:bg-neutral-400/30' : ''
                } ${isMobile ? 'h-9 w-9 p-0' : ''}`}
                onClick={toggleAudioControls}
                data-audio-toggle="true"
              >
                <span className="relative">
                  <Music2 className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                </span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent side="left" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
            <span className="font-medium tracking-wide">Music control</span>
          </TooltipContent>
        </Tooltip>

        {/* Stats button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={!isMobile ? {
                scale: 1.1,
                boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                backgroundColor: "rgba(255, 255, 255, 0.12)"
              } : {}}
              whileTap={{
                scale: 0.95,
                backgroundColor: "rgba(255, 255, 255, 0.18)",
                rotate: isMobile ? 0 : 90
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17,
                rotate: {
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }
              }}
              className="rounded-full overflow-hidden"
            >
              <Button
                variant="outline"
                size={isMobile ? "default" : "icon"}
                className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 cursor-pointer ${
                  isStatsPopoverOpen ? 'bg-neutral-300/30 dark:bg-neutral-400/30' : ''
                } ${isMobile ? 'h-9 w-9 p-0' : ''}`}
                onClick={() => setIsStatsPopoverOpen(true)}
              >
                <span className="relative">
                  <BarChart3 className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                </span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent side="left" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
            <span className="font-medium tracking-wide">Focus statistics</span>
          </TooltipContent>
        </Tooltip>

        {/* Settings button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={!isMobile ? {
                scale: 1.1,
                boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                backgroundColor: "rgba(255, 255, 255, 0.12)"
              } : {}}
              whileTap={{
                scale: 0.95,
                backgroundColor: "rgba(255, 255, 255, 0.18)",
                rotate: isMobile ? 0 : 90
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17,
                rotate: {
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }
              }}
              className="rounded-full overflow-hidden"
            >
              <Button
                variant="outline"
                size={isMobile ? "default" : "icon"}
                className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 group cursor-pointer ${
                  isSettingsDialogOpen ? 'bg-neutral-300/30 dark:bg-neutral-400/30' : ''
                } ${isMobile ? 'h-9 w-9 p-0' : ''}`}
                onClick={toggleSettingsDialog}
                data-timer-settings-trigger
              >
                <span className="relative">
                  <Settings className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                </span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent side="left" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
            <span className="font-medium tracking-wide">Timer settings</span>
          </TooltipContent>
        </Tooltip>

        {/* Fullscreen button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={!isMobile ? {
                scale: 1.1,
                boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
                backgroundColor: "rgba(255, 255, 255, 0.12)"
              } : {}}
              whileTap={{
                scale: 0.95,
                backgroundColor: "rgba(255, 255, 255, 0.18)",
                rotate: isMobile ? 0 : 90
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17,
                rotate: {
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }
              }}
              className="rounded-full overflow-hidden"
            >
              <Button
                variant="outline"
                size={isMobile ? "default" : "icon"}
                className={`rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-200 group cursor-pointer ${
                  isMobile ? 'h-9 w-9 p-0' : ''
                }`}
                onClick={handleFullscreen}
              >
                <span className="relative">
                  <Maximize className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white drop-shadow-md`} />
                </span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent side="left" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
            <span className="font-medium tracking-wide">Toggle fullscreen</span>
          </TooltipContent>
        </Tooltip>
            </motion.div>
          ) : (
            showCurrentTime && (
              <motion.div
                key="current-time-display"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{
                  duration: 0.35,
                  ease: [0.4, 0, 0.2, 1] // Same easing as navigation controls
                }}
              >
                <div
                  className={cn(
                    "flex items-center gap-1.5 sm:gap-2 bg-black/10 backdrop-blur-lg border border-white/40 rounded-lg shadow-lg shadow-black/20",
                    "px-2 sm:px-2.5 py-1 sm:py-1.5 text-white font-mono font-medium tabular-nums",
                    isMobile ? "text-xs" : "text-sm"
                  )}
                  role="status"
                  aria-label={`Current time: ${currentTime}`}
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 20,
                      ease: "linear",
                      repeat: Infinity
                    }}
                  >
                    <Clock className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white/90 flex-shrink-0 drop-shadow-md`} />
                  </motion.div>
                  <span className="text-white/95 drop-shadow-md">
                    {currentTime}
                  </span>
                </div>
              </motion.div>
            )
          )}
        </AnimatePresence>
      </div>

      {/* Navigation buttons - Desktop only (mobile uses swipe gestures) */}
      {!isMobile && canNavigate && hasPrevious && (
        <div
          className={`fixed left-4 top-1/2 -translate-y-1/2 z-50 transition-opacity duration-300 ${
            showControls ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.div
                whileHover={{
                  scale: 1.15,
                  x: -5,
                  boxShadow: "0 0 16px rgba(255, 255, 255, 0.7)",
                  backgroundColor: "rgba(255, 255, 255, 0.15)"
                }}
                whileTap={{
                  scale: 0.9,
                  x: -8
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }}
                className="rounded-full overflow-hidden"
              >
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-300 cursor-pointer h-10 w-10 p-0"
                  onClick={navigateToPreviousVideo}
                  disabled={isNavigating || isVideoSwitching}
                >
                  <ChevronLeft className="h-6 w-6 text-white drop-shadow-lg" />
                </Button>
              </motion.div>
            </TooltipTrigger>
            <TooltipContent side="right" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
              <div className="flex items-center gap-2">
                <span className="font-medium tracking-wide whitespace-nowrap">Previous Video</span>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      {/* Right Navigation Button - Desktop only */}
      {!isMobile && canNavigate && hasNext && (
        <div
          className={`fixed right-4 top-1/2 -translate-y-1/2 z-50 transition-opacity duration-300 ${
            showControls ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.div
                whileHover={{
                  scale: 1.15,
                  x: 5,
                  boxShadow: "0 0 16px rgba(255, 255, 255, 0.7)",
                  backgroundColor: "rgba(255, 255, 255, 0.15)"
                }}
                whileTap={{
                  scale: 0.9,
                  x: 8
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }}
                className="rounded-full overflow-hidden"
              >
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-full bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30 transition-all duration-300 cursor-pointer h-10 w-10 p-0"
                  onClick={navigateToNextVideo}
                  disabled={isNavigating || isVideoSwitching}
                >
                  <ChevronRight className="h-6 w-6 text-white drop-shadow-lg" />
                </Button>
              </motion.div>
            </TooltipTrigger>
            <TooltipContent side="left" sideOffset={8} className="bg-background/90 backdrop-blur-sm text-foreground">
              <div className="flex items-center gap-2">
                <span className="font-medium tracking-wide whitespace-nowrap">Next Video</span>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      {/* Timer Settings Dialog */}
      <TimerSettings
        isOpen={isSettingsDialogOpen}
        onOpenChange={handleSettingsDialogClose}
      />

      {/* Task Sheet */}
      <TaskSheet
        isOpen={isTaskSheetOpen}
        onClose={() => setIsTaskSheetOpen(false)}
        onTaskFocusStart={() => setIsTaskSheetOpen(false)}
      />

      {/* Quick Stats Dialog */}
      <QuickStatsDialog
        isOpen={isStatsPopoverOpen}
        onOpenChange={setIsStatsPopoverOpen}
        quickStatsData={quickStatsData}
        isLoading={statsLoading}
        error={statsError}
      />
    </>
  );
});